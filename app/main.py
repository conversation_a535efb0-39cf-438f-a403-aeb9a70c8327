import sys
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict
import os
import logging
import yaml
import json
from dotenv import load_dotenv
from app.repo import <PERSON><PERSON><PERSON><PERSON><PERSON>, LocalDirectoryError, GitOperationError
from app.llm import get_llm_generator
import asyncio
from time import time as current_time  # Rename to avoid conflict with the time module
import time  # Import the full time module for sleep
import argparse
import webbrowser
import socket
import httpx
from app.utils.code_analyzer import CodeAnalyzer
from app.utils.language_detector import detect_language
import threading
import itertools
from uvicorn import Config, Server
import psutil
from colorama import Fore, Back, Style, init
from app.documentation.template_handler import TemplateDocGenerator
import tkinter as tk
from tkinter import filedialog

# Initialize colorama
init(autoreset=True)

# Custom formatter for colored logs
class ColoredFormatter(logging.Formatter):
    """Custom formatter for colored logs"""
    FORMAT = "%(levelname)s:%(name)s:%(message)s"

    FORMATS = {
        logging.DEBUG: Style.DIM + FORMAT + Style.RESET_ALL,
        logging.INFO: Fore.GREEN + FORMAT + Style.RESET_ALL,
        logging.WARNING: Fore.YELLOW + FORMAT + Style.RESET_ALL,
        logging.ERROR: Fore.RED + FORMAT + Style.RESET_ALL,
        logging.CRITICAL: Fore.RED + Back.WHITE + FORMAT + Style.RESET_ALL
    }

    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt)
        return formatter.format(record)

# Set up logging with colored output
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(ColoredFormatter())
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Load environment variables
load_dotenv()

# Check for required API keys
api_keys = {
    "GROQ_API_KEY": os.getenv("GROQ_API_KEY"),
    "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
    "GEMINI_API_KEY": os.getenv("GOOGLE_GEMINI_API_KEY")  # Fixed to match the actual env var name
}

# Log API key status
for key_name, key_value in api_keys.items():
    if key_value:
        masked_key = f"{key_value[:4]}...{key_value[-4:]}" if len(key_value) > 8 else "***"
        logger.info(f"{key_name} is set with value: {masked_key}")
    else:
        logger.warning(f"{key_name} is not set")

if not any(api_keys.values()):
    logger.error("No API keys found. At least one API key (GROQ_API_KEY, OPENAI_API_KEY, or GEMINI_API_KEY) must be set")

# Load configuration
with open('config/config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# Initialize FastAPI app
app = FastAPI(title="Code Documentation Generator API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize components
repo_handler = RepoHandler(config['git']['temp_dir'])
code_analyzer = CodeAnalyzer()

# Initialize the LLM generator
try:
    # We'll initialize the provider later when the user selects it
    llm_generator = None
    logger.info("LLM provider will be selected by the user")
except Exception as e:
    logger.error(f"Failed to initialize LLM generator: {e}")
    llm_generator = TemplateDocGenerator(
        templates_dir=config['documentation']['templates_dir'],
        output_dir=config['documentation']['output_dir']
    )
    logger.warning("Falling back to template-based generator due to initialization error")

# Use the selected generator
doc_generator = llm_generator

# Global variable to store the token
auth_token = None

class DocRequest(BaseModel):
    repo_url: Optional[str] = None
    local_dir: Optional[str] = None
    access_token: Optional[str] = None

@app.get("/")
async def root():
    return {
        "message": "Welcome to the Code Documentation Generator API",
        "endpoints": {
            "github_login": "/auth/login/github",
            "gitlab_login": "/auth/login/gitlab"
        }
    }

@app.get("/auth/github/callback")
async def github_callback(request: Request):
    """Handle GitHub OAuth callback."""
    global auth_token
    try:
        code = request.query_params.get("code")
        if not code:
            return {"error": "Authorization code not found in the callback response"}

        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                "https://github.com/login/oauth/access_token",
                headers={"Accept": "application/json"},
                data={
                    "client_id": os.getenv("GITHUB_CLIENT_ID"),
                    "client_secret": os.getenv("GITHUB_CLIENT_SECRET"),
                    "code": code,
                    "redirect_uri": "http://localhost:8000/auth/github/callback"
                }
            )
            token_response.raise_for_status()
            token_data = token_response.json()

        access_token = token_data.get("access_token")
        if not access_token:
            return {"error": "Failed to retrieve access token from GitHub"}

        # Save the token to the global variable and .env file
        auth_token = access_token
        save_token_to_env(access_token)  # Save the token to the .env file
        load_dotenv(override=True)  # Reload .env variables to make the new token available immediately
        logger.info("Successfully obtained and stored access token.")

        # Return JSON response to the browser
        return {
            "message": "Successfully obtained access token",
            "access_token": access_token
        }
    except Exception as e:
        logger.error(f"Error handling GitHub callback: {e}")
        return {"error": "Failed to handle callback"}

@app.post("/generate-comments/")
async def generate_comments(file_content: str, file_path: str):
    """
    Generate Doxygen-compatible comments for a given file.

    Args:
        file_content: The content of the file
        file_path: The path to the file

    Returns:
        JSON response with generated comments
    """
    language = detect_language(file_path)
    code_analyzer = CodeAnalyzer()
    file_structure = code_analyzer.get_file_structure(file_path)

    doc_generator = TemplateDocGenerator(
        templates_dir=config['documentation']['templates_dir'],
        output_dir=config['documentation']['output_dir']
    )

    comments = await doc_generator.generate_inline_comments(
        code=file_content,
        language=language,
        context=file_structure
    )

    return {"comments": comments}

def is_server_running(host="localhost", port=8000):
    """Check if the FastAPI server is running."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.connect((host, port))
            return True
        except ConnectionRefusedError:
            return False

def is_port_in_use(port):
    """Check if a port is already in use."""
    for conn in psutil.net_connections(kind='inet'):
        if conn.laddr.port == port:
            return True
    return False

def save_token_to_env(token):
    """Save the token to the .env file, replacing the existing GITHUB_TOKEN if present."""
    env_file = os.path.join(os.getcwd(), ".env")
    lines = []
    token_updated = False
    try:
        # Read existing lines
        if os.path.exists(env_file):
            with open(env_file, "r") as f:
                lines = f.readlines()

        # Update or add the GITHUB_TOKEN line
        with open(env_file, "w") as f:
            for line in lines:
                if line.strip().startswith("GITHUB_TOKEN="):
                    f.write(f"GITHUB_TOKEN={token}\n")
                    token_updated = True
                else:
                    f.write(line)
            if not token_updated:
                f.write(f"\nGITHUB_TOKEN={token}\n")  # Add if not found

        logger.info("Token saved/updated in .env file.")
    except Exception as e:
        logger.error(f"Failed to save token to .env file: {e}")

def get_token_from_env():
    """Retrieve the token from environment variables (after loading .env)."""
    load_dotenv(override=True)  # Ensure latest .env is loaded
    token = os.getenv("GITHUB_TOKEN")
    if token:
        logger.info("Token retrieved from environment variables.")
        return token
    logger.warning("No GITHUB_TOKEN found in environment variables or .env file.")
    return None

def wait_for_auth_token(timeout=60):
    """Wait for the token to be set by the FastAPI server and saved to .env."""
    global auth_token
    logger.info("Waiting for authentication token...")
    start_time = current_time()
    while current_time() - start_time < timeout:
        # Check if the token is set in the global variable (set by callback)
        if auth_token:
            logger.info("Authentication token obtained (from global var).")
            return auth_token

        # Check if the token is saved in the .env file
        load_dotenv(override=True)  # Reload .env dynamically
        token = os.getenv("GITHUB_TOKEN")
        if token:
            logger.info("Authentication token retrieved from .env file.")
            auth_token = token  # Update global var as well
            return token

        time.sleep(2)  # Retry every 2 seconds

    logger.error("Timed out waiting for authentication token.")
    return None

def parse_args():
    parser = argparse.ArgumentParser(description="AI-powered Repository Analyzer")
    source_group = parser.add_mutually_exclusive_group()
    source_group.add_argument("--repo-url", help="Git repository URL")
    source_group.add_argument("--local-dir", help="Local directory path")
    parser.add_argument("--auth-token", help="Authentication token (OAuth or PAT, only needed for Git repositories)")
    args = parser.parse_args()

    if not args.repo_url and not args.local_dir:
        source_type = input("Do you want to process a Git repository or a local directory? (git/local): ").strip().lower()
        if source_type in ['git', 'g']:
            args.repo_url = input("Enter repository URL: ").strip()
        elif source_type in ['local', 'l']:
            print("Please select a local directory using the pop-up window.")
            args.local_dir = select_directory()
            if not args.local_dir:
                print("No directory selected. Exiting.")
                sys.exit(1)
        else:
            print("Invalid option. Defaulting to Git repository.")
            args.repo_url = input("Enter repository URL: ").strip()

    args.source = args.local_dir if args.local_dir else args.repo_url
    args.is_local = bool(args.local_dir)
    return args

def handle_git_authentication(args):
    """Handle Git repository authentication separately after server is started if needed."""
    if args.repo_url and not args.auth_token:
        # Try to get the token from the environment variables first
        token = get_token_from_env()
        if token:
            print("Found existing authentication token.")
            args.auth_token = token
        else:
            print("No auth token found. Attempting to authenticate via browser...")
            client_id = os.getenv('GITHUB_CLIENT_ID')
            if not client_id:
                print("GitHub Client ID not found in environment variables.")
            else:
                auth_url = f"https://github.com/login/oauth/authorize?client_id={client_id}&redirect_uri=http://localhost:8000/auth/github/callback"
                webbrowser.open(auth_url)
                print("\nA browser window should have opened for GitHub authentication.")
                print("After authenticating, you'll be redirected to a page with the access token.")

            # Wait for the token to be saved to .env by the callback
            token = wait_for_auth_token()
            if token:
                args.auth_token = token
            else:
                print("Authentication failed or timed out. Please enter your GitHub Personal Access Token (PAT):")
                manual_token = input("Enter your GitHub token: ").strip()
                if manual_token:
                    args.auth_token = manual_token
                    save_token_to_env(manual_token)  # Save the manually entered token to the .env file
                else:
                    print("No token provided. The application may not be able to access private repositories.")
    return args

def show_spinner(message):
    """Show a spinner in the CLI to indicate progress."""
    for char in itertools.cycle(["|", "/", "-", "\\"]):
        if not spinner_active:
            break
        sys.stdout.write(f"\r{message} {char}")
        sys.stdout.flush()
        time.sleep(0.1)
    sys.stdout.write("\r")

# Global variable for spinner control
global spinner_active
spinner_active = False

def stop_spinner():
    """Stop the spinner."""
    global spinner_active
    spinner_active = False

def select_directory():
    """!
    @brief Open a dialog to select a directory path.
    @return The selected directory path or None if canceled.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main tkinter window
    directory = filedialog.askdirectory(title="Select a Directory")
    return directory

def select_llm_provider():
    """!
    @brief Prompt the user to select an LLM provider from available options.
    @return The selected provider name.
    """
    # Get available providers based on API keys
    available_providers = []
    if os.getenv("GROQ_API_KEY"):
        available_providers.append(("groq", "Groq (deepseek-r1-distill-llama-70b)"))
    if os.getenv("OPENAI_API_KEY"):
        available_providers.append(("openai", "OpenAI (gpt-4)"))
    if os.getenv("GOOGLE_GEMINI_API_KEY"):
        available_providers.append(("gemini", "Google Gemini 2.0 Flash"))
    if os.getenv("OPENROUTER_API_KEY"):
        available_providers.append(("openrouter", "OpenRouter (meta-ai/llama-3.1-8b-instruct)"))

    if not available_providers:
        logger.error("No API keys found for any supported LLM provider")
        return None

    # If only one provider is available, use it without asking
    if len(available_providers) == 1:
        provider_id, provider_name = available_providers[0]
        print(f"Using the only available LLM provider: {provider_name}")
        return provider_id

    # Display available providers
    print("\nAvailable LLM providers:")
    for i, (provider_id, provider_name) in enumerate(available_providers, 1):
        print(f"{i}. {provider_name}")

    # Get user selection
    while True:
      try:
        selection = input("\nSelect a provider (enter number): ").strip()
        index = int(selection) - 1
        if 0 <= index < len(available_providers):
          provider_id, provider_name = available_providers[index]
          print(f"Selected provider: {provider_name}")
          return provider_id
        else:
          print(f"Invalid selection. Please enter a number between 1 and {len(available_providers)}.")
      except ValueError:
        print("Please enter a valid number.")
      except KeyboardInterrupt:
        print("\nSelection cancelled. Using default provider.")
        return available_providers[0][0] # Return the first available provider

# Function to start the FastAPI server in a separate thread
def start_server():
    config = Config(app, host="127.0.0.1", port=8000, log_level="info")
    server = Server(config)
    server.run()

def start_server_if_needed(args):
    """Start the FastAPI server only if needed for Git repository OAuth authentication."""
    if not args.is_local and not args.auth_token:
        # Only start server for Git repos that need OAuth authentication
        if is_port_in_use(8000):
            logger.error("Port 8000 is already in use. Please stop the process using it or use a different port.")
            sys.exit(1)

        logger.info("Starting FastAPI server for OAuth authentication...")
        server_thread = threading.Thread(target=start_server, daemon=True)
        server_thread.start()
        time.sleep(2)  # Give the server some time to start
        return server_thread
    return None

# Main execution block
if __name__ == "__main__":
    # Parse arguments first to determine if we need the server
    args = parse_args()

    # Start server only if needed (for Git repos without auth token)
    server_thread = start_server_if_needed(args)

    # Handle Git authentication if needed (after server is started)
    if not args.is_local:
        args = handle_git_authentication(args)

    # Prompt user to select an LLM provider
    provider = select_llm_provider()
    if not provider:
        logger.error("No LLM provider available. Please check your API keys.")
        sys.exit(1)

    # Initialize the selected LLM generator
    try:
        doc_generator = get_llm_generator(provider)
        logger.info(f"Using {provider} LLM for comment generation")
    except Exception as e:
        logger.error(f"Failed to initialize {provider} LLM generator: {e}")
        doc_generator = TemplateDocGenerator(
            templates_dir=config['documentation']['templates_dir'],
            output_dir=config['documentation']['output_dir']
        )
        logger.warning("Falling back to template-based generator due to initialization error")

    try:
        if args.is_local:
            logger.info(f"Starting local directory analysis for: {args.source}")

            # Process the local directory
            spinner_active = True
            spinner_thread = threading.Thread(target=show_spinner, args=("Processing local directory...",))
            spinner_thread.daemon = True
            spinner_thread.start()

            try:
                # Copy the directory to a temporary location
                temp_dir = repo_handler.process_local_directory(args.source)
                logger.info(f"Directory copied to temporary location: {temp_dir}")

                # Get the file structure
                file_structure = repo_handler.get_file_structure(temp_dir)
                logger.info(f"Found {len(file_structure)} files to analyze")

                # Stop the spinner
                spinner_active = False
                spinner_thread.join(timeout=1)

                # Process each file
                print(f"\nAnalyzing {len(file_structure)} files...")

                output_dir = "data/generated-output"
                # Create a unique subfolder for each analysis
                import datetime
                current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                output_dir = os.path.join(output_dir, f"analysis_{current_time}")
                os.makedirs(output_dir, exist_ok=True)

                processed_files = 0
                for file_info in file_structure:
                    file_path = file_info['full_path']
                    rel_path = file_info['path']

                    try:
                        # Read the file content
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            file_content = f.read()

                        # Detect language
                        language = detect_language(file_path)
                        if language == 'Unknown':
                            logger.warning(f"Skipping file with unknown language: {rel_path}")
                            continue

                        print(f"Processing {rel_path} ({language})...")
                        logger.info(f"🔄 PROCESSING FILE: {rel_path} - Language: {language}, Size: {len(file_content)} chars")

                        # Generate comments
                        logger.info(f"🤖 CALLING LLM for {rel_path}...")
                        comments = asyncio.run(doc_generator.generate_inline_comments(
                            code=file_content,
                            language=language
                        ))
                        logger.info(f"🎯 LLM RESPONSE for {rel_path}: {len(comments) if comments else 0} comments received")

                        if comments:
                            logger.info(f"💬 INSERTING COMMENTS into {rel_path}: {len(comments)} comments")
                            logger.debug(f"💬 COMMENTS TO INSERT:\n{json.dumps(comments, indent=2)}")

                            # Insert comments into the file
                            updated_content = repo_handler.insert_comments(
                                content=file_content,
                                comments=comments,
                                file_path=file_path,
                                comment_style='doxygen'
                            )

                            # Save the updated file
                            output_file_path = os.path.join(output_dir, rel_path)
                            os.makedirs(os.path.dirname(output_file_path), exist_ok=True)

                            with open(output_file_path, 'w', encoding='utf-8') as f:
                                f.write(updated_content)

                            logger.info(f"✅ SAVED {rel_path} with {len(comments)} comments to: {output_file_path}")
                            processed_files += 1
                        else:
                            logger.warning(f"No comments generated for {rel_path}")

                    except Exception as e:
                        logger.error(f"Error processing file {rel_path}: {e}")

                print(f"\nProcessing complete! Generated comments for {processed_files} files.")
                print(f"Commented files saved to: {os.path.abspath(output_dir)}")

            except Exception as e:
                spinner_active = False
                if spinner_thread.is_alive():
                    spinner_thread.join(timeout=1)
                logger.error(f"Error during local directory analysis: {e}")

        else:
            logger.info(f"Starting repository analysis for: {args.source}")
            # TODO: Implement repository analysis
            print("Repository analysis is not yet implemented.")
    except Exception as e:
        logger.error(f"Error: {e}")
