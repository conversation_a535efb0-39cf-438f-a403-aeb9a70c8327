#!/usr/bin/env python3
"""
MCP Server for AI Agent Browser Tools
A Model Context Protocol server that provides browser and web content tools.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Sequence
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource,
        Tool,
        TextContent,
        ImageContent,
        EmbeddedResource,
        LoggingLevel
    )
except ImportError:
    print("MCP SDK not installed. Please install with: pip install mcp", file=sys.stderr)
    sys.exit(1)

import aiohttp
import asyncio
from urllib.parse import urlparse
import markdownify

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ai-agent-mcp-server")

# Initialize the MCP server
server = Server("ai-agent-browser-tools")

class BrowserTools:
    """Browser tools for web content fetching and analysis."""
    
    def __init__(self):
        self.session = None
    
    async def start(self):
        """Initialize HTTP session."""
        if not self.session:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'Mozilla/5.0 (compatible; AI-Agent-Browser/1.0)'}
            )
    
    async def stop(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def fetch_webpage(self, url: str, format_type: str = "markdown") -> Dict[str, Any]:
        """Fetch webpage content."""
        await self.start()
        
        try:
            async with self.session.get(url) as response:
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {response.reason}")
                
                html_content = await response.text()
                
                if format_type == "markdown":
                    content = markdownify.markdownify(
                        html_content,
                        heading_style="ATX",
                        strip=['script', 'style']
                    )
                else:
                    content = html_content
                
                # Extract title
                import re
                title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
                title = title_match.group(1).strip() if title_match else "Untitled"
                
                return {
                    "success": True,
                    "url": url,
                    "title": title,
                    "content": content,
                    "format": format_type,
                    "status_code": response.status
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url
            }

# Global browser tools instance
browser_tools = BrowserTools()

@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available tools."""
    return [
        Tool(
            name="fetch_webpage",
            description="Fetch and read content from a webpage",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "The URL to fetch content from"
                    },
                    "format": {
                        "type": "string",
                        "enum": ["markdown", "html"],
                        "default": "markdown",
                        "description": "Output format for the content"
                    }
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="analyze_url",
            description="Analyze a URL and extract basic information",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "The URL to analyze"
                    }
                },
                "required": ["url"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls."""
    try:
        if name == "fetch_webpage":
            url = arguments.get("url")
            format_type = arguments.get("format", "markdown")
            
            if not url:
                return [TextContent(type="text", text="Error: URL is required")]
            
            result = await browser_tools.fetch_webpage(url, format_type)
            
            if result["success"]:
                response = f"# {result['title']}\n\n**URL:** {result['url']}\n\n{result['content']}"
                return [TextContent(type="text", text=response)]
            else:
                return [TextContent(type="text", text=f"Error fetching webpage: {result['error']}")]
        
        elif name == "analyze_url":
            url = arguments.get("url")
            
            if not url:
                return [TextContent(type="text", text="Error: URL is required")]
            
            parsed = urlparse(url)
            analysis = f"""URL Analysis:
- **Domain:** {parsed.netloc}
- **Path:** {parsed.path}
- **Scheme:** {parsed.scheme}
- **Query:** {parsed.query}
- **Fragment:** {parsed.fragment}
"""
            return [TextContent(type="text", text=analysis)]
        
        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]
            
    except Exception as e:
        logger.error(f"Error in tool {name}: {e}")
        return [TextContent(type="text", text=f"Error executing tool {name}: {str(e)}")]

@server.list_resources()
async def handle_list_resources() -> List[Resource]:
    """List available resources."""
    return [
        Resource(
            uri="ai-agent://browser-tools/help",
            name="Browser Tools Help",
            description="Help documentation for browser tools",
            mimeType="text/markdown"
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """Read a resource."""
    if uri == "ai-agent://browser-tools/help":
        return """# AI Agent Browser Tools

This MCP server provides browser and web content tools for AI agents.

## Available Tools:

### fetch_webpage
Fetch and read content from a webpage.
- **url** (required): The URL to fetch
- **format** (optional): Output format ('markdown' or 'html', default: 'markdown')

### analyze_url
Analyze a URL and extract basic information.
- **url** (required): The URL to analyze

## Usage Examples:

1. Fetch a webpage as markdown:
   ```
   fetch_webpage(url="https://example.com", format="markdown")
   ```

2. Analyze a URL:
   ```
   analyze_url(url="https://example.com/path?query=value")
   ```
"""
    else:
        raise ValueError(f"Unknown resource: {uri}")

async def main():
    """Main entry point for the MCP server."""
    # Setup cleanup
    async def cleanup():
        await browser_tools.stop()
    
    try:
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="ai-agent-browser-tools",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )
    finally:
        await cleanup()

if __name__ == "__main__":
    asyncio.run(main())
